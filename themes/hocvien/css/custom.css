/**
 * NukeViet Content Management System
 * @version 4.x
 * <AUTHOR> <<EMAIL>>
 * @copyright (C) 2009-2021 VINADES.,JSC. All rights reserved
 * @license GNU/GPL version 2 or any later version
 * @see https://github.com/nukeviet The NukeViet CMS GitHub project
 */

/* Các tùy chỉnh CSS của giao diện nên để vào đây */

.nav>li>a {
    position: relative;
    display: block;
    padding: 10px;
}

.navbar-default .navbar-collapse,
.navbar-default .navbar-form {
    border: none;
}

.navbar-default {
    background-color: transparent;
    border-color: transparent;
}

.navbar-default .navbar-nav>li>a {
    color: #222;
    text-transform: uppercase;
    font-weight: 500;
}

.navbar-default .navbar-nav>.active>a,
.navbar-default .navbar-nav>.active>a:hover,
.navbar-default .navbar-nav>.active>a:focus {
    color: #be1010;
    background-color: transparent;
}

.navbar-default .navbar-nav>li>a:hover,
.navbar-default .navbar-nav>li>a:focus {
    color: #be1010;
    background-color: transparent;
}

.navbar-default .navbar-nav>.open>a,
.navbar-default .navbar-nav>.open>a:hover,
.navbar-default .navbar-nav>.open>a:focus {
    background-color: transparent;
    color: #be1010;
}

.footer-info-connect {
    padding: 15px 0;
    background-image: linear-gradient(to right, #be8f108c, #cd5a0ac9, #b88006cc, #9300009e);
}

.footer-info-connect,
.footer-info-connect a {
    color: #fff;
}

.footer-info-connect>.wraper.container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: nowrap;
}

.footer-info-connect>.wraper.container .box-item-info {
    display: flex;
    align-items: center;
    gap: 30px;
}

.footer-info-connect>.wraper.container .box-item-info:first-child {
    margin-right: 150px;
}

.footer-info-connect .icon-svg>img {
    filter: brightness(0) invert(1);
}

.footer-info-connect .icon-svg .fa {
    font-size: 45px;
}

.footer-info-connect>.wraper.container .box-item-info .info-text>span {
    display: block;
    font-size: 16px;
    font-weight: 400;
}

.footer-info-connect>.wraper.container .box-item-info .info-text>span:last-child {
    font-size: 20px;
    font-weight: 700;
}

footer .box-copyright {
    background-color: #930000;
    font-size: 12px;
    color: #fff;
    padding: 5px 0;
    font-weight: 300;
}

footer .box-copyright>.wraper.container {
    display: flex;
    flex-wrap: wrap;
}

footer .box-copyright a {
    color: #fff;
}

.ml-auto {
    margin-left: auto;
}

footer .row {
    margin-left: -20px;
    margin-right: -20px;
}

footer .row>div {
    padding-left: 20px;
    padding-right: 20px;
}

.breadcrumbs-search {
    background-color: #f6f6f6;
    border-radius: 6px;
    padding: 5px 8px 5px 15px;
    margin-bottom: 15px;
    min-height: 46.79px;
}

.breadcrumbs-search>.row {
    display: flex;
    align-items: center;
}

.footer-info-connect .social-list {
    display: flex;
    align-items: center;
    gap: 35px;
}

/* About Us */
#about-us {
    padding: 50px 0;
}

#about-us .box-about-top {
    text-align: center;
}

#about-us .box-about-top h2 {
    font-size: 24px;
    font-weight: 100;
}

#about-us .box-about-top h3 {
    font-size: 32px;
    text-transform: uppercase;
    margin-bottom: 25px;
}

.section-title {
    font-weight: 600;
    margin-bottom: 15px;
    text-align: center;
    font-size: 1.25rem;
    color: #2c3e50;
    position: relative;
    padding-bottom: 10px;
}

.section-title i {
    color: #3498db;
    margin-right: 8px;
}

.content-box {
    border: 1px solid #ccc;
    min-height: 200px;
    padding: 20px;
    background-color: #f8f9fa;
    white-space: pre-wrap;
    box-shadow: 0 2px 6px rgb(0 0 0 / 0.1);
    border-radius: 8px;
}

.content-box img {
    max-width: 100%;
    height: auto;
    margin-top: 15px;
    border-radius: 6px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

@media (min-width: 768px) {
    .navbar-nav>li>a {
        padding-top: 0;
        padding-bottom: 0;
    }
}